"use client";

import React, { useEffect, useState } from "react";
import { fetchAllDisbursements, fetchAllGrants, updateDisbursementStatus } from "@/services/disbursement-service";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { toast } from "sonner";
import { motion } from "framer-motion";
import {
  Filter,
  Download,
  FileText,
  Calendar,
  Eye,
  TrendingUp,
  Clock,
  CheckCircle,
  AlertCircle,
} from "lucide-react";

interface Disbursement {
  id: number;
  grant: number;
  grant_name: string;
  tranche_number: string;
  scheduled_date: string;
  amount: string;
  remarks: string;
  status: string;
  request_letter_url?: string;
  acknowledgement_letter_url?: string;
}

interface Grant {
  id: number;
  grant_name: string;
}

const statusOptions = [
  { value: "yet_to_be_disbursed", label: "Yet to be Disbursed", color: "bg-amber-100 text-amber-800 border-amber-200" },
  { value: "disbursed", label: "Disbursed", color: "bg-emerald-100 text-emerald-800 border-emerald-200" },
  { value: "on_hold", label: "On Hold", color: "bg-red-100 text-red-800 border-red-200" },
];

const getStatusIcon = (status: string) => {
  switch (status) {
    case "disbursed":
      return <CheckCircle className="h-3 w-3" />;
    case "on_hold":
      return <AlertCircle className="h-3 w-3" />;
    default:
      return <Clock className="h-3 w-3" />;
  }
};

export default function DisbursementTable() {
  const [disbursements, setDisbursements] = useState<Disbursement[]>([]);
  const [grants, setGrants] = useState<Grant[]>([]);
  const [selectedGrant, setSelectedGrant] = useState("all");
  const [selectedStatus, setSelectedStatus] = useState("all");
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const loadData = async () => {
      try {
        setIsLoading(true);
        const [disbursementsData, grantsData] = await Promise.all([
          fetchAllDisbursements(),
          fetchAllGrants()
        ]);
        setDisbursements(disbursementsData);
        setGrants(grantsData);
      } catch (error) {
        toast.error("Failed to load data");
      } finally {
        setIsLoading(false);
      }
    };
    loadData();
  }, []);

  const handleStatusChange = async (id: number, newStatus: string) => {
    try {
      await updateDisbursementStatus(id.toString(), newStatus);
      setDisbursements(prev =>
        prev.map(item => (item.id === id ? { ...item, status: newStatus } : item))
      );
      toast.success("Status updated successfully");
    } catch (error) {
      toast.error("Failed to update status");
    }
  };

  const filtered = disbursements.filter(item => {
    const grantMatch = selectedGrant === "all" || item.grant === parseInt(selectedGrant);
    const statusMatch = selectedStatus === "all" || item.status === selectedStatus;
    return grantMatch && statusMatch;
  });

  const totalAmount = filtered.reduce((sum, item) => sum + parseFloat(item.amount || "0"), 0);

  const handleExport = () => {
    if (filtered.length === 0) {
      toast.error("No data to export");
      return;
    }

    const csvHeaders = [
      "Grant Name",
      "Tranche Number",
      "Scheduled Date",
      "Amount (₹)",
      "Request Letter",
      "Acknowledgment Letter",
      "Remarks",
      "Status"
    ];

    const csvData = filtered.map(item => [
      item.grant_name || "",
      item.tranche_number || "",
      item.scheduled_date || "",
      parseFloat(item.amount || "0").toLocaleString("en-IN"),
      item.request_letter_url ? "Available" : "Not Available",
      item.acknowledgement_letter_url ? "Available" : "Not Available",
      item.remarks || "",
      statusOptions.find(opt => opt.value === item.status)?.label || item.status
    ]);

    const csvContent = [
      csvHeaders.join(","),
      ...csvData.map(row => row.map(cell => `"${cell}"`).join(","))
    ].join("\n");

    const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" });
    const link = document.createElement("a");
    const url = URL.createObjectURL(blob);
    link.setAttribute("href", url);
    link.setAttribute("download", `disbursement-history-${new Date().toISOString().split('T')[0]}.csv`);
    link.style.visibility = "hidden";
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    toast.success("Disbursement data exported successfully");
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse">
          <div className="h-4 bg-gray-200 rounded w-1/4 mb-4"></div>
          <div className="h-32 bg-gray-200 rounded"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">

      {/* Filters Section */}
      <motion.div
        className="bg-white p-6 rounded-xl shadow-sm border border-gray-100"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.4, delay: 0.1 }}
      >
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-4">
          <div className="flex items-center gap-2">
            <div className="p-1.5 bg-teal-50 rounded-md">
              <Filter className="h-4 w-4 text-teal-500" />
            </div>
            <h3 className="text-lg font-semibold text-gray-800">Filter Disbursements</h3>
          </div>
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={handleExport}
              className="flex items-center gap-1 border-teal-200 text-teal-700 hover:bg-teal-50"
            >
              <Download className="h-4 w-4" />
              Export
            </Button>
          </div>
        </div>

        <div className="flex flex-col sm:flex-row items-start sm:items-end gap-4">
          <div className="flex flex-col sm:flex-row gap-4 flex-1">
            <div className="space-y-2 flex-1">
              <label className="text-sm font-medium text-gray-700 flex items-center gap-2">
                <FileText className="h-4 w-4 text-teal-500" />
                Grant
              </label>
              <Select value={selectedGrant} onValueChange={setSelectedGrant}>
                <SelectTrigger className="w-[200px] border-teal-200 focus:ring-teal-500">
                  <SelectValue placeholder="Select grant" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Grants</SelectItem>
                  {grants.map((grant) => (
                    <SelectItem key={grant.id} value={grant.id.toString()}>
                      {grant.grant_name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2 flex-1">
              <label className="text-sm font-medium text-gray-700 flex items-center gap-2">
                <Calendar className="h-4 w-4 text-teal-500" />
                Status
              </label>
              <Select value={selectedStatus} onValueChange={setSelectedStatus}>
                <SelectTrigger className="w-[180px] border-teal-200 focus:ring-teal-500">
                  <SelectValue placeholder="Select status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Statuses</SelectItem>
                  {statusOptions.map(option => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>
      </motion.div>

      {/* Main Table */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.4, delay: 0.2 }}
      >
        <Card className="shadow-sm border border-gray-100 rounded-xl overflow-hidden bg-white hover:shadow-md transition-shadow duration-300">
          <div className="h-0.5 bg-gradient-to-r from-[#00998F] via-teal-500 to-emerald-400"></div>

          <CardContent className="p-0">
            <div className="overflow-x-auto">
              <div className="rounded-lg border-0 shadow-sm overflow-hidden">
                <div className="max-h-[600px] overflow-y-auto">
                  <table className="w-full border-collapse bg-white min-w-[1400px]">
                    <thead className="sticky top-0 z-10">
                      <tr className="bg-gradient-to-r from-slate-800 via-slate-700 to-slate-800 text-white border-b border-slate-600 shadow-lg">
                        <th className="px-6 py-4 text-left text-xs font-bold text-white uppercase tracking-wider border-r border-slate-600/50">
                          <div className="flex items-center gap-2">
                            <FileText className="h-4 w-4" />
                            Grant
                          </div>
                        </th>
                        <th className="px-2 py-2 text-center text-xs font-bold text-white uppercase tracking-wider border-r border-slate-600/50 w-20">
                          <div className="flex items-center justify-center gap-2">
                            <TrendingUp className="h-3 w-3" />
                            Tranche
                          </div>
                        </th>
                        <th className="px-2 py-2 text-center text-xs font-bold text-white uppercase tracking-wider border-r border-slate-600/50 w-24">
                          <div className="flex items-center justify-center gap-2">
                            <Calendar className="h-4 w-4" />
                            Date
                          </div>
                        </th>
                        <th className="px-2 py-2 text-center text-xs font-bold text-white uppercase tracking-wider border-r border-slate-600/50 w-28">
                          <div className="flex items-center justify-center gap-2">
                            Amount
                          </div>
                        </th>
                        <th className="px-2 py-2 text-center text-xs font-bold text-white uppercase tracking-wider border-r border-slate-600/50 w-24">
                          Request Letter
                        </th>
                        <th className="px-2 py-2 text-center text-xs font-bold text-white uppercase tracking-wider border-r border-slate-600/50 w-24">
                          Ack Letter
                        </th>
                        <th className="px-6 py-4 text-center text-xs font-bold text-white uppercase tracking-wider border-r border-slate-600/50">
                          Remarks
                        </th>
                        <th className="px-2 py-2 text-center text-xs font-bold text-white uppercase tracking-wider w-20">
                          Status
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-100">
                      {filtered.length === 0 ? (
                        <tr>
                          <td colSpan={8} className="px-6 py-12 text-center text-gray-500">
                            <div className="flex flex-col items-center gap-2">
                              <FileText className="h-8 w-8 text-gray-300" />
                              <p className="text-sm">No disbursements found</p>
                            </div>
                          </td>
                        </tr>
                      ) : (
                        filtered.map((item, index: number) => (
                          <motion.tr
                            key={item.id}
                            className="hover:bg-teal-50/50 transition-colors duration-200 border-b border-gray-100"
                            initial={{ opacity: 0, y: 10 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.3, delay: index * 0.05 }}
                          >
                            <td className="px-6 py-4 text-sm font-medium text-gray-900 border-r border-gray-100">
                              <div className="flex items-center gap-2">
                                <div className="w-2 h-2 rounded-full bg-teal-500"></div>
                                {item.grant_name}
                              </div>
                            </td>
                            <td className="px-4 py-4 text-sm text-gray-700 text-center border-r border-gray-100">
                              <Badge variant="secondary" className="bg-teal-100 text-teal-800 border-teal-200">
                                {item.tranche_number}
                              </Badge>
                            </td>
                            <td className="px-4 py-4 text-sm text-gray-700 text-center border-r border-gray-100">
                              <div className="flex items-center justify-center gap-1">
                                <Calendar className="h-3 w-3 text-gray-400" />
                                {item.scheduled_date}
                              </div>
                            </td>
                            <td className="px-4 py-4 text-sm font-semibold text-gray-900 text-center border-r border-gray-100">
                              <div className="flex items-center justify-center gap-1">
                                <span className="text-teal-600">₹</span>
                                {parseFloat(item.amount || "0").toLocaleString("en-IN")}
                              </div>
                            </td>
                            <td className="px-4 py-4 text-sm text-center border-r border-gray-100">
                              {item.request_letter_url ? (
                                <Button
                                  variant="outline"
                                  size="sm"
                                  className="border-teal-200 text-teal-700 hover:bg-teal-50"
                                  onClick={() => window.open(item.request_letter_url, '_blank')}
                                >
                                  <Eye className="h-3 w-3 mr-1" />
                                  View
                                </Button>
                              ) : (
                                <span className="text-gray-400">—</span>
                              )}
                            </td>
                            <td className="px-3 py-4 text-sm text-center border-r border-gray-100">
                              {item.acknowledgement_letter_url ? (
                                <Button
                                  variant="outline"
                                  size="sm"
                                  className="border-teal-200 text-teal-700 hover:bg-teal-50"
                                  onClick={() => window.open(item.acknowledgement_letter_url, '_blank')}
                                >
                                  <Eye className="h-3 w-3 mr-1" />
                                  View
                                </Button>
                              ) : (
                                <span className="text-gray-400">—</span>
                              )}
                            </td>
                            <td className="px-6 py-4 text-sm text-gray-700 border-r border-gray-100">
                              <div className="truncate max-w-xs" title={item.remarks}>
                                {item.remarks || "—"}
                              </div>
                            </td>
                            <td className="px-4 py-4 text-sm text-center">
                              <Select
                                value={item.status}
                                onValueChange={(value) => handleStatusChange(item.id, value)}
                              >
                                <SelectTrigger className="w-full border-gray-200 focus:ring-teal-500 text-xs">
                                  <div className="flex items-center gap-2">
                                    {getStatusIcon(item.status)}
                                    <SelectValue />
                                  </div>
                                </SelectTrigger>
                                <SelectContent>
                                  {statusOptions.map(option => (
                                    <SelectItem key={option.value} value={option.value}>
                                      <div className="flex items-center gap-2">
                                        {getStatusIcon(option.value)}
                                        {option.label}
                                      </div>
                                    </SelectItem>
                                  ))}
                                </SelectContent>
                              </Select>
                            </td>
                          </motion.tr>
                        ))
                      )}
                    </tbody>
                    {filtered.length > 0 && (
                      <tfoot className="bg-gradient-to-r from-teal-50 to-emerald-50 border-t-2 border-teal-200">
                        <tr>
                          <td colSpan={3} className="px-6 py-4 text-right text-sm font-bold text-teal-800">
                            Total Amount:
                          </td>
                          <td className="px-6 py-4 text-center text-lg font-bold text-teal-900">
                            <div className="flex items-center justify-center gap-1">
                              <span className="text-teal-600">₹</span>
                              {totalAmount.toLocaleString("en-IN")}
                            </div>
                          </td>
                          <td colSpan={4} className="px-6 py-4 text-sm text-teal-700">
                            <div className="flex items-center gap-2">
                              <TrendingUp className="h-4 w-4" />
                              {filtered.length} disbursement{filtered.length !== 1 ? 's' : ''} found
                            </div>
                          </td>
                        </tr>
                      </tfoot>
                    )}
                  </table>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </motion.div>
    </div>
  );
}
