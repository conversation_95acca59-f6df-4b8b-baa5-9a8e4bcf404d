"use client";

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Button } from '@/components/ui/button';
import Layout from '@/components/grantmaker/Layout';
import { motion } from 'framer-motion';
import { Search, Filter, ArrowUpDown, Download, Plus } from 'lucide-react';
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer, LineChart, Line } from 'recharts';
import { FundingSummaryCard } from '@/components/funding/FundingSummaryCard';
import { AnimatedLoader } from '@/components/grantmaker/AnimatedLoader';
import DisbursementTable from '@/components/funding/DisbursementTable';
import { CurrencyDollarIcon, ChartBarIcon, WalletIcon } from '@/components/ui/icons';

// Import the services for data fetching
import { formatCurrency } from '@/services/grantmaker/grantmaker-service';
import { DisbursementHistory, DisbursementSummary, MonthlyDisbursement } from '@/services/funding-service';
import { fetchAllGrants } from '@/services/disbursement-service';

export default function GrantmakerDisbursementsPage() {
  const router = useRouter();
  const [selectedYear, setSelectedYear] = useState('2024');
  const [selectedQuarter, setSelectedQuarter] = useState('All');
  const [selectedStatus, setSelectedStatus] = useState('All');
  const [selectedGrant, setSelectedGrant] = useState('All');
  const [grants, setGrants] = useState<Array<{id: number, grant_name: string}>>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isLoadingGrants, setIsLoadingGrants] = useState(true);
  const [disbursementData, setDisbursementData] = useState<{
    summary: DisbursementSummary;
    monthlyDisbursements: MonthlyDisbursement[];
    disbursementHistory: DisbursementHistory[];
  } | null>(null);

  // Mock data for development - this would be replaced with actual API calls
  const mockDisbursementData = {
    summary: {
      totalDisbursed: 850000,
      remainingBalance: 250000,
      pendingDisbursements: 150000,
    },
    monthlyDisbursements: [
      { month: 'Jan', value: 85000 },
      { month: 'Feb', value: 88000 },
      { month: 'Mar', value: 92000 },
      { month: 'Apr', value: 87000 },
      { month: 'May', value: 91000 },
      { month: 'Jun', value: 89000 },
      { month: 'Jul', value: 94000 },
      { month: 'Aug', value: 90000 },
      { month: 'Sep', value: 85000 },
      { month: 'Oct', value: 49000 },
      { month: 'Nov', value: 0 },
      { month: 'Dec', value: 0 },
    ],
    disbursementHistory: [
      {
        id: '1',
        date: '2024-01-15',
        amount: 85000,
        status: 'Disbursed',
        remark: 'Q1 operational funding',
        acknowledgement: 'Completed',
        grantName: 'Education First Foundation',
      },
      {
        id: '2',
        date: '2024-02-10',
        amount: 88000,
        status: 'Disbursed',
        remark: 'Program implementation phase 1',
        acknowledgement: 'Completed',
        grantName: 'Healthcare Initiative',
      },
      {
        id: '3',
        date: '2024-03-05',
        amount: 92000,
        status: 'Disbursed',
        remark: 'Community outreach funding',
        acknowledgement: 'Completed',
        grantName: 'Community Development Trust',
      },
      {
        id: '4',
        date: '2024-04-20',
        amount: 87000,
        status: 'Disbursed',
        remark: 'Environmental project funding',
        acknowledgement: 'Completed',
        grantName: 'Green Earth Project',
      },
      {
        id: '5',
        date: '2024-05-12',
        amount: 91000,
        status: 'Disbursed',
        remark: 'Arts program funding',
        acknowledgement: 'Completed',
        grantName: 'Arts & Culture Foundation',
      },
      {
        id: '6',
        date: '2024-10-30',
        amount: 150000,
        status: 'Pending',
        remark: 'Q4 operational funding',
        acknowledgement: 'Pending',
        grantName: 'Education First Foundation',
      },
    ],
  };

  // Fetch disbursement data
  useEffect(() => {
    const fetchDisbursementData = async () => {
      setIsLoading(true);
      try {
        // In a real implementation, this would be an API call
        // const data = await fundingService.getDisbursementData(selectedYear, selectedQuarter, selectedStatus);
        // setDisbursementData(data);

        // Using mock data for now
        setTimeout(() => {
          setDisbursementData(mockDisbursementData);
          setIsLoading(false);
        }, 1000);
      } catch (error) {
        console.error('Error fetching disbursement data:', error);
        setIsLoading(false);
      }
    };

    fetchDisbursementData();
  }, [selectedYear, selectedQuarter, selectedStatus, selectedGrant]);

  // Load grants data
  useEffect(() => {
    const loadGrants = async () => {
      try {
        setIsLoadingGrants(true);
        const grantsData = await fetchAllGrants();
        setGrants(grantsData);
      } catch (error) {
        console.error('Error fetching grants:', error);
      } finally {
        setIsLoadingGrants(false);
      }
    };
    loadGrants();
  }, []);

  // Calculate percentages for the disbursement progress
  const totalFunding = disbursementData ?
    (disbursementData.summary.totalDisbursed + disbursementData.summary.remainingBalance) : 0;

  const disbursementPercentage = totalFunding ?
    Math.round((disbursementData?.summary.totalDisbursed || 0) / totalFunding * 100) : 0;

  // Custom tooltip for charts
  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-white p-3 border border-gray-200 shadow-lg rounded-lg">
          <p className="font-semibold text-gray-800">{`${label}`}</p>
          {payload.map((entry: any, index: number) => (
            <p key={index} style={{ color: entry.color }} className="text-sm">
              {`${entry.name}: ${formatCurrency(entry.value)}`}
            </p>
          ))}
        </div>
      );
    }
    return null;
  };

  if (isLoading) {
    return (
      <Layout title="Disbursement Management">
        <AnimatedLoader type="disbursement" message="Processing disbursement data..." />
      </Layout>
    );
  }

  return (
    <Layout title="Disbursement Management">
      <div className="space-y-8 pb-8">
        <div className="bg-gradient-to-r from-orange-50 to-amber-50 p-6 rounded-lg shadow-sm mb-8">
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.5 }}
            >
              <h1 className="text-2xl font-bold text-gray-800">Disbursement Overview</h1>
              <p className="text-gray-600 mt-1">Track and manage all grantee disbursements</p>
            </motion.div>
            <div className="flex flex-col sm:flex-row gap-4">
              <motion.div
                className="flex items-center"
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3, delay: 0.1 }}
              >
                <span className="text-sm mr-2 text-gray-600">Year</span>
                <Select value={selectedYear} onValueChange={setSelectedYear}>
                  <SelectTrigger className="w-[150px] border-orange-200 focus:ring-orange-500">
                    <SelectValue placeholder="Select year" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="2024">2024</SelectItem>
                    <SelectItem value="2023">2023</SelectItem>
                  </SelectContent>
                </Select>
              </motion.div>
              <motion.div
                className="flex items-center"
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3, delay: 0.2 }}
              >
                <span className="text-sm mr-2 text-gray-600">Quarter</span>
                <Select value={selectedQuarter} onValueChange={setSelectedQuarter}>
                  <SelectTrigger className="w-[150px] border-orange-200 focus:ring-orange-500">
                    <SelectValue placeholder="Select quarter" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="All">All Quarters</SelectItem>
                    <SelectItem value="Q1">Q1</SelectItem>
                    <SelectItem value="Q2">Q2</SelectItem>
                    <SelectItem value="Q3">Q3</SelectItem>
                    <SelectItem value="Q4">Q4</SelectItem>
                  </SelectContent>
                </Select>
              </motion.div>
              <motion.div
                className="flex items-center"
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3, delay: 0.3 }}
              >
                <span className="text-sm mr-2 text-gray-600">Grant</span>
                <Select value={selectedGrant} onValueChange={setSelectedGrant} disabled={isLoadingGrants}>
                  <SelectTrigger className="w-[200px] border-orange-200 focus:ring-orange-500">
                    <SelectValue placeholder={isLoadingGrants ? "Loading grants..." : "Select grant"} />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="All">All Grants</SelectItem>
                    {grants.map((grant) => (
                      <SelectItem key={grant.id} value={grant.id.toString()}>
                        {grant.grant_name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </motion.div>
              <motion.div
                className="flex items-center"
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3, delay: 0.4 }}
              >
                <span className="text-sm mr-2 text-gray-600">Status</span>
                <Select value={selectedStatus} onValueChange={setSelectedStatus}>
                  <SelectTrigger className="w-[150px] border-orange-200 focus:ring-orange-500">
                    <SelectValue placeholder="Select status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="All">All Statuses</SelectItem>
                    <SelectItem value="Disbursed">Disbursed</SelectItem>
                    <SelectItem value="Pending">Pending</SelectItem>
                    <SelectItem value="Failed">Failed</SelectItem>
                  </SelectContent>
                </Select>
              </motion.div>
            </div>
          </div>
        </div>

        {/* Summary Cards */}
        <motion.div
          className="grid grid-cols-1 md:grid-cols-3 gap-6"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ staggerChildren: 0.1 }}
        >
          <FundingSummaryCard
            title="Total Disbursed"
            amount={formatCurrency(disbursementData?.summary.totalDisbursed || 0)}
            icon={<WalletIcon className="w-5 h-5" />}
            accentColor="orange"
            description={`${disbursementPercentage}% of total funding disbursed`}
          />

          <FundingSummaryCard
            title="Remaining Balance"
            amount={formatCurrency(disbursementData?.summary.remainingBalance || 0)}
            icon={<CurrencyDollarIcon className="w-5 h-5" />}
            accentColor="amber"
            description="Funds remaining to be disbursed"
          />

          <FundingSummaryCard
            title="Pending Disbursements"
            amount={formatCurrency(disbursementData?.summary.pendingDisbursements || 0)}
            icon={<ChartBarIcon className="w-5 h-5" />}
            accentColor="orange"
            description="Disbursements awaiting processing"
          />
        </motion.div>

        {/* Charts Section */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mt-8">
          {/* Monthly Disbursement Chart */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.4 }}
            className="lg:col-span-2"
          >
            <Card className="shadow-md border-0 rounded-lg overflow-hidden h-full">
              <div className="h-1 bg-gradient-to-r from-orange-500 to-amber-400"></div>
              <CardHeader>
                <CardTitle className="text-xl font-semibold text-gray-800">Monthly Disbursement Trends</CardTitle>
                <CardDescription>Disbursement amounts by month</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-80">
                  <ResponsiveContainer width="100%" height="100%">
                    <LineChart
                      data={disbursementData?.monthlyDisbursements}
                      margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                    >
                      <CartesianGrid strokeDasharray="3 3" opacity={0.3} />
                      <XAxis dataKey="month" tick={{ fill: '#6B7280' }} />
                      <YAxis
                        tickFormatter={(value) => `₹${value / 1000}k`}
                        tick={{ fill: '#6B7280' }}
                      />
                      <Tooltip content={<CustomTooltip />} />
                      <Legend />
                      <Line
                        type="monotone"
                        dataKey="value"
                        name="Disbursed Amount"
                        stroke="#FF9800"
                        strokeWidth={2}
                        dot={{ r: 4, fill: '#FF9800' }}
                        activeDot={{ r: 6 }}
                      />
                    </LineChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        </div>

        {/* Disbursement History Table */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.4, delay: 0.2 }}
          className="mt-8"
        >
          <Card className="shadow-md border-0 rounded-lg overflow-hidden">
            <div className="h-1 bg-gradient-to-r from-orange-500 to-amber-400"></div>
            <CardHeader>
              <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center">
                <div>
                  <CardTitle className="text-xl font-semibold text-gray-800">Disbursement History</CardTitle>
                  <CardDescription>Detailed list of all disbursements</CardDescription>
                </div>
                <div className="flex gap-2 mt-2 sm:mt-0">
                  <Button variant="outline" size="sm" className="flex items-center gap-1">
                    <Filter className="h-4 w-4" />
                    Filter
                  </Button>
                  <Button variant="outline" size="sm" className="flex items-center gap-1">
                    <ArrowUpDown className="h-4 w-4" />
                    Sort
                  </Button>
                  <Button variant="outline" size="sm" className="flex items-center gap-1">
                    <Download className="h-4 w-4" />
                    Export
                  </Button>
                  <Button variant="outline" size="sm" className="flex items-center gap-1">
                    <Plus className="h-4 w-4" />
                    New Disbursement
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <DisbursementTable />
            </CardContent>
          </Card>
        </motion.div>

        {/* Action Buttons */}
        <div className="flex flex-wrap gap-4 mt-8">
          <Button
            onClick={() => router.push('/grantmaker/funding')}
            className="bg-orange-600 hover:bg-orange-700"
          >
            Back to Funding Overview
          </Button>
          <Button
            onClick={() => router.push('/grantmaker/funding/expenses')}
            className="bg-amber-600 hover:bg-amber-700"
          >
            View Expenses
          </Button>
          <Button
            onClick={() => router.push('/grantmaker/funding/summary')}
            className="bg-orange-500 hover:bg-orange-600"
          >
            View Detailed Summary
          </Button>
        </div>
      </div>
    </Layout>
  );
}